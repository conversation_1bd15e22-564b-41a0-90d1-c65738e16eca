const $noteTargetEl = document.getElementById('note-modal');
const noteOptions = {
    placement: 'center',
    backdrop: 'dynamic',
    backdropClasses: 'bg-gray-900 bg-opacity-50 dark:bg-opacity-80 fixed inset-0 z-40',
    closable: true,
    onHide: () => {
        console.log('save note');
        activeArrowKey = true;
        save_note();
    },
    onShow: () => {
        console.log('modal is shown');
    },
    onToggle: () => {
        console.log('modal has been toggled');
    }
};
const noteModal = new Modal($noteTargetEl, noteOptions);

$('.close-note').click(function (e) {
    noteModal.hide();
});

$(document).on('click', '.note-btn', function(e) {
    const id = $(this).attr('data-id');
    const qid = $(this).attr('data-qid');
    question_note(id, qid);
});

function question_note(id, qid) {
    noteModal.show();
    $('#note-modal .shimmer').removeClass('hidden');
    $('#note_updated_at').addClass('hidden');
    $('#note_updated_at_time').html('');
    $('#note').val('');
    $('#note').addClass('hidden');
    $('#delete-note').addClass('hidden');
    $('#note_id').val(id);
    $('#question_id').val(qid);
    if (id != 0) {
        $.ajax({
            type: "GET",
            url: base_url + "note/get_note/" + id,
            beforeSend: function () {
            },
            success: function (json) {
                var data = JSON.parse(json);
                console.log(data);
                if (data.status == "success") {
                    fill_note(data.data);
                } else {
                    // noteModal.hide();
                    $('#alert-error').fadeIn();
                    setTimeout(() => {
                        $('#alert-error').fadeOut();
                    }, 5000);
                }
            },
            error: function (xhr, status, strErr) {
                // noteModal.hide();
                $('#alert-error').fadeIn();
                setTimeout(() => {
                    $('#alert-error').fadeOut();
                }, 5000);
            }
        });
    } else {
        $('#note-modal .shimmer').addClass('hidden');
        $('#note').removeClass('hidden');
    }
}

function fill_note(data) {
    $('#note').val(data.note);
    $('#note_updated_at_time').html(timeSince(data.updated_time));
    $('#note_updated_at').removeClass('hidden');
    $('#delete_note').removeClass('hidden');
    $('#note').removeClass('hidden');
    $('#note-modal .shimmer').addClass('hidden');
}

function save_note() {
    const note = $('#note').val();
    const noteId = $('#note_id').val();
    const qid = $('#question_id').val();
    if (note != "") {
        $.ajax({
            type: "POST",
            url: base_url + "note/save_note",
            data: {
                note: note, id: noteId, qid: qid
            },
            beforeSend: function () {
            },
            success: function (json) {
                var data = JSON.parse(json);
                console.log(data);
                if (data.status == "success") {
                    const qid = $('#question_id').val();
                    $('.note' + qid).attr('data-id', data.data);
                    $('.note' + qid).find('.note').addClass('hidden');
                    $('.note' + qid).find('.noted').removeClass('hidden');
                } else {
                    $('#alert-error').fadeIn();
                    setTimeout(() => {
                        $('#alert-error').fadeOut();
                    }, 5000);
                }
            },
            error: function (xhr, status, strErr) {
                $('#alert-error').fadeIn();
                setTimeout(() => {
                    $('#alert-error').fadeOut();
                }, 5000);
            }
        });
    }
}

function delete_note() {
    const noteId = $('#note_id').val();
    const qid = $('#question_id').val();
    $.ajax({
        type: "POST",
        url: base_url + "note/delete_note",
        data: {
            id: noteId
        },
        beforeSend: function () {
        },
        success: function (json) {
            var data = JSON.parse(json);
            if (data.status == "success") {
                $('.note' + qid).find('.note').removeClass('hidden');
                $('.note' + qid).find('.noted').addClass('hidden');
                $('.note' + qid).attr('data-id', 0);
                $('#note').val('');
                noteModal.hide();
            } else {
                $('#alert-error').fadeIn();
                setTimeout(() => {
                    $('#alert-error').fadeOut();
                }, 5000);
            }
        },
        error: function (xhr, status, strErr) {
            $('#alert-error').fadeIn();
            setTimeout(() => {
                $('#alert-error').fadeOut();
            }, 5000);
        }
    });
}
function getDuration(seconds) {
    var epoch, interval;

    for (var i = 0; i < DURATION_IN_SECONDS.epochs.length; i++) {
        epoch = DURATION_IN_SECONDS.epochs[i];
        text = DURATION_IN_SECONDS.text[i];
        interval = Math.floor(seconds / DURATION_IN_SECONDS[epoch]);
        if (interval >= 1) {
            return {
                interval: interval,
                epoch: epoch,
                text: text,
            };
        } else {
            return {
                interval: '',
                epoch: epoch,
                text: DURATION_IN_SECONDS.today,
            };
        }
    }
};

function timeSince(date) {
    var seconds = Math.floor((new Date() - new Date(date)) / 1000);
    var duration = getDuration(seconds);
    var suffix = ((duration.interval > 1 || duration.interval === 0) && DURATION_IN_SECONDS.text[0] == 'year') ? 's' : '';
    return duration.interval == '' ? duration.text : duration.interval + ' ' + duration.text + suffix + ' ' + DURATION_IN_SECONDS.ago;
};